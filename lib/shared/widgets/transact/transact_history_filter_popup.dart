import 'package:flutter/material.dart';
import 'package:wd/core/models/apis/transact.dart';
import 'package:wd/core/models/entities/statement_filter_way_entity.dart';
import 'package:wd/core/models/entities/top_up_record_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class TransactFilterItem {
  String name;
  String code;
  String parentCode;
  bool isSel;

  TransactFilterItem({required this.name, required this.code, required this.isSel, this.parentCode = ""});

  factory TransactFilterItem.fromStatementFilterWay(StatementFilterWay model) {
    return TransactFilterItem(
      name: model.changeWayName,
      code: model.changeWayCode,
      isSel: model.isSel,
    );
  }

  factory TransactFilterItem.fromStatementFilterType(StatementFilterType model) {
    return TransactFilterItem(
      name: model.changeTypeName,
      code: model.changeTypeCode,
      parentCode: model.changeWayCode,
      isSel: model.isSel,
    );
  }

  factory TransactFilterItem.fromTransactFilterWay(TransactFilterWay model) {
    return TransactFilterItem(
      name: model.wayName,
      code: model.wayCode,
      isSel: model.isSel,
    );
  }

  factory TransactFilterItem.fromTransactFilterType(TransactFilterType model) {
    return TransactFilterItem(
      name: model.typeName,
      code: model.typeCode,
      parentCode: model.wayCode,
      isSel: model.isSel,
    );
  }
}

class TransactHistoryFilterPopup {
  static Future<void> show({
    required BuildContext context,
    required TransactType type,
    required List<TransactFilterItem> filterWayList,
    required List<TransactFilterItem> filterTypeList,
    required VoidCallback onClickSure,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.5),
      builder: (context) => _TransactHistoryFilterPopupContent(
        type: type,
        filterWayList: filterWayList,
        filterTypeList: filterTypeList,
        onClickSure: onClickSure,
      ),
    );
  }
}

class _TransactHistoryFilterPopupContent extends StatefulWidget {
  final TransactType type;
  final List<TransactFilterItem> filterWayList;
  final List<TransactFilterItem> filterTypeList;
  final VoidCallback onClickSure;

  const _TransactHistoryFilterPopupContent({
    required this.type,
    required this.filterWayList,
    required this.filterTypeList,
    required this.onClickSure,
  });

  @override
  State<_TransactHistoryFilterPopupContent> createState() => _TransactHistoryFilterPopupContentState();
}

class _TransactHistoryFilterPopupContentState extends State<_TransactHistoryFilterPopupContent> {
  List<TransactFilterItem> filterWayList = [];
  List<TransactFilterItem> filterTypeList = [];

  String filterWayTitle = "";
  String filterTypeTitle = "";

  String currentWayCode = ""; // 当前选中的交易方式的code

  @override
  void initState() {
    filterWayList = List.from(widget.filterWayList);
    filterTypeList = List.from(widget.filterTypeList);

    switch (widget.type) {
      case TransactType.topUp:
        filterWayTitle = "Transaction Method";
        filterTypeTitle = "Transaction Type";
        break;
      case TransactType.withdraw:
        filterWayTitle = "Transaction Method";
        filterTypeTitle = "Transaction Type";
        break;
      case TransactType.statement:
        filterWayTitle = "Transaction Method";
        filterTypeTitle = "Transaction Type";
        break;
    }
    super.initState();
  }

  onTapFilterWayListItem(TransactFilterItem model) {
    /// 单选
    for (TransactFilterItem element in filterWayList) {
      element.isSel = false;
    }
    model.isSel = true;
    setState(() {
      currentWayCode = model.code;
    });
  }

  onTapFilterTypeListItem(TransactFilterItem model) {
    /// 单选
    for (TransactFilterItem element in filterTypeList) {
      element.isSel = false;
    }
    model.isSel = true;
    setState(() {});
  }

  onClickResetAction() {
    onTapFilterWayListItem(filterWayList.first);
    onTapFilterTypeListItem(filterTypeList.first);
  }

  Widget _buildFilterItem({
    required String title,
    required bool isSel,
    required GestureTapCallback onTap,
  }) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 8.gw),
        margin: EdgeInsets.only(right: 8.gw, bottom: 8.gw),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.gw),
          color: isSel ? context.colorTheme.tabItemBgA : context.colorTheme.borderB,
          border: Border.all(
            color: isSel ? context.colorTheme.borderE : Colors.transparent,
            width: 1.gw,
          ),
        ),
        child: AneText(
          title,
          style: TextStyle(
            color: isSel ? context.colorTheme.textPrimary : context.colorTheme.textTitle,
            fontSize: 14.gw,
            fontWeight: isSel ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Row(
      children: [
        Expanded(
          child: CommonButton(
            title: "Reset",
            onPressed: () => onClickResetAction(),
            height: 48.gw,
            radius: 12.gw,
            backgroundColor: context.colorTheme.btnBgSecondary,
            style: CommonButtonStyle.secondary,
            borderColor: Colors.transparent,
          ),
        ),
        SizedBox(width: 16.gw),
        Expanded(
          child: CommonButton(
            title: "Confirm",
            onPressed: () {
              widget.onClickSure();
              Navigator.of(context).pop();
            },
            height: 48.gw,
            radius: 12.gw,
            style: CommonButtonStyle.primary,
            borderColor: Colors.transparent,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: const Alignment(1.2, -0.6),
      child: Material(
        type: MaterialType.transparency,
        child: Container(
          width: 350.gw,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          margin: EdgeInsets.symmetric(horizontal: 20.gw),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.gw),
            color: context.colorTheme.foregroundColor,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                padding: EdgeInsets.all(20.gw),
                child: AneText(
                  filterWayTitle,
                  style: TextStyle(
                    color: context.colorTheme.btnTitleSecondary,
                    fontSize: 18.gw,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              // Content
              Flexible(
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: 20.gw),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Transaction Method Section
                      Wrap(
                        children: filterWayList
                            .map((e) => _buildFilterItem(
                                  title: e.name,
                                  isSel: e.isSel,
                                  onTap: () => onTapFilterWayListItem(e),
                                ))
                            .toList(),
                      ),

                      SizedBox(height: 24.gw),

                      // Transaction Type Section
                      AneText(
                        filterTypeTitle,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18.gw,
                          fontWeight: FontWeight.w600,
                        ),
                      ),

                      SizedBox(height: 16.gw),

                      Wrap(
                        children: filterTypeList
                            .where((type) => type.parentCode == currentWayCode || type.name == "全部")
                            .map((e) => _buildFilterItem(
                                title: e.name, isSel: e.isSel, onTap: () => onTapFilterTypeListItem(e)))
                            .toList(),
                      ),
                    ],
                  ),
                ),
              ),

              // Bottom buttons
              Container(
                padding: EdgeInsets.all(20.gw),
                child: _buildBottomButtons(),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
